// models/UsageTracking.js
import mongoose from 'mongoose';

/**
 * Schema for tracking user actions and usage for subscription limits
 */
const UsageTrackingSchema = new mongoose.Schema({
  // User who performed the action
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // Type of action performed
  actionType: {
    type: String,
    required: true,
    enum: [
      'pdfUploads',
      'businessPlans', 
      'investorPitches',
      'businessQA',
      'chatMessages',
      'aiCharacters'
    ],
    index: true
  },
  
  // Additional metadata about the action
  metadata: {
    endpoint: String,
    method: String,
    fileSize: Number,
    fileName: String,
    success: {
      type: Boolean,
      default: true
    },
    errorMessage: String
  },
  
  // When the action was performed
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // User's plan at the time of action
  planName: {
    type: String,
    default: 'Starter'
  }
}, {
  timestamps: true,
  collection: 'usage_tracking'
});

// Compound indexes for efficient querying
UsageTrackingSchema.index({ userId: 1, actionType: 1, createdAt: -1 });
UsageTrackingSchema.index({ userId: 1, createdAt: -1 });
UsageTrackingSchema.index({ actionType: 1, createdAt: -1 });

// Static method to get usage count for a user and action type within a time period
UsageTrackingSchema.statics.getUsageCount = async function(userId, actionType, startDate, endDate = new Date()) {
  try {
    const count = await this.countDocuments({
      userId,
      actionType,
      createdAt: {
        $gte: startDate,
        $lte: endDate
      },
      'metadata.success': { $ne: false } // Only count successful actions
    });
    
    return count;
  } catch (error) {
    console.error('Error getting usage count:', error);
    return 0;
  }
};

// Static method to get daily usage
UsageTrackingSchema.statics.getDailyUsage = async function(userId, actionType, date = new Date()) {
  const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000 - 1);
  
  return this.getUsageCount(userId, actionType, startOfDay, endOfDay);
};

// Static method to get monthly usage
UsageTrackingSchema.statics.getMonthlyUsage = async function(userId, actionType, date = new Date()) {
  const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
  const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999);
  
  return this.getUsageCount(userId, actionType, startOfMonth, endOfMonth);
};

// Static method to get total usage (all time)
UsageTrackingSchema.statics.getTotalUsage = async function(userId, actionType) {
  const startOfTime = new Date(0);
  return this.getUsageCount(userId, actionType, startOfTime);
};

// Static method to record usage
UsageTrackingSchema.statics.recordUsage = async function(userId, actionType, metadata = {}, planName = 'Starter') {
  try {
    const usage = await this.create({
      userId,
      actionType,
      metadata: {
        ...metadata,
        success: true
      },
      planName
    });
    
    return usage;
  } catch (error) {
    console.error('Error recording usage:', error);
    throw error;
  }
};

// Static method to get user's usage summary
UsageTrackingSchema.statics.getUserUsageSummary = async function(userId, date = new Date()) {
  try {
    const actionTypes = ['pdfUploads', 'businessPlans', 'investorPitches', 'businessQA', 'chatMessages', 'aiCharacters'];
    
    const summary = {
      daily: {},
      monthly: {},
      total: {}
    };
    
    for (const actionType of actionTypes) {
      summary.daily[actionType] = await this.getDailyUsage(userId, actionType, date);
      summary.monthly[actionType] = await this.getMonthlyUsage(userId, actionType, date);
      summary.total[actionType] = await this.getTotalUsage(userId, actionType);
    }
    
    return summary;
  } catch (error) {
    console.error('Error getting user usage summary:', error);
    throw error;
  }
};

// Static method to clean up old usage records (for maintenance)
UsageTrackingSchema.statics.cleanupOldRecords = async function(daysToKeep = 365) {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const result = await this.deleteMany({
      createdAt: { $lt: cutoffDate }
    });
    
    console.log(`Cleaned up ${result.deletedCount} old usage records`);
    return result.deletedCount;
  } catch (error) {
    console.error('Error cleaning up old records:', error);
    throw error;
  }
};

const UsageTracking = mongoose.model('UsageTracking', UsageTrackingSchema);

export default UsageTracking;
