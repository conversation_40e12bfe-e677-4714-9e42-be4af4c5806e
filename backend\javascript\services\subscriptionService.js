// services/subscriptionService.js
import SubscriptionLimits from '../models/SubscriptionLimits.js';
import User from '../models/User.js';
import UsageTracking from '../models/UsageTracking.js';

/**
 * Service for handling subscription limits and usage tracking
 */
class SubscriptionService {
    
    /**
     * Get user's current subscription plan
     */
    static async getUserPlan(userId) {
        try {
            const user = await User.findById(userId);
            if (!user) {
                throw new Error('User not found');
            }
            
            // Default to Starter plan if no subscription is set
            const planName = user.subscription?.planName || 'Starter';
            return planName;
        } catch (error) {
            console.error('Error getting user plan:', error);
            return 'Starter'; // Default fallback
        }
    }

    /**
     * Get limits for a specific plan
     */
    static async getPlanLimits(planName) {
        try {
            const plan = await SubscriptionLimits.findOne({ 
                planName, 
                isActive: true 
            });
            
            if (!plan) {
                // Return default limits if plan not found
                const defaultData = SubscriptionLimits.getDefaultLimits(planName);
                return defaultData.limits;
            }
            
            return plan.limits;
        } catch (error) {
            console.error('Error getting plan limits:', error);
            // Return very restrictive limits as fallback
            return {
                pdfUploads: { monthly: 1, total: -1 },
                businessPlans: { monthly: 1, total: -1 },
                investorPitches: { monthly: 1, total: -1 },
                businessQA: { daily: 1, monthly: 10 },
                chatMessages: { daily: 5, monthly: 50 },
                aiCharacters: { total: 1 },
                storage: { total: 50 },
                advancedAnalytics: false,
                prioritySupport: false,
                customBranding: false
            };
        }
    }

    /**
     * Get user's current limits based on their subscription
     */
    static async getUserLimits(userId) {
        try {
            const planName = await this.getUserPlan(userId);
            const limits = await this.getPlanLimits(planName);
            return { planName, limits };
        } catch (error) {
            console.error('Error getting user limits:', error);
            throw error;
        }
    }

    /**
     * Check if user can perform a specific action
     */
    static async canUserPerformAction(userId, actionType, period = 'monthly') {
        try {
            const { limits } = await this.getUserLimits(userId);
            
            // Get current usage for the user
            const currentUsage = await this.getCurrentUsage(userId, actionType, period);
            
            // Get the limit for this action
            const actionLimits = limits[actionType];
            if (!actionLimits) {
                console.warn(`No limits defined for action: ${actionType}`);
                return false;
            }
            
            const limit = actionLimits[period];
            
            // -1 means unlimited
            if (limit === -1) {
                return true;
            }
            
            // Check if user has exceeded the limit
            return currentUsage < limit;
        } catch (error) {
            console.error('Error checking user action permission:', error);
            return false; // Deny access on error for security
        }
    }

    /**
     * Get current usage for a user and action type
     */
    static async getCurrentUsage(userId, actionType, period) {
        try {
            const now = new Date();

            switch (period) {
                case 'daily':
                    return await UsageTracking.getDailyUsage(userId, actionType, now);
                case 'monthly':
                    return await UsageTracking.getMonthlyUsage(userId, actionType, now);
                case 'total':
                    return await UsageTracking.getTotalUsage(userId, actionType);
                default:
                    return await UsageTracking.getMonthlyUsage(userId, actionType, now);
            }
        } catch (error) {
            console.error('Error getting current usage:', error);
            return 999; // Return high number to deny access on error
        }
    }

    /**
     * Record usage for tracking
     */
    static async recordUsage(userId, actionType, metadata = {}) {
        try {
            // Get user's current plan
            const planName = await this.getUserPlan(userId);

            // Record the usage
            await UsageTracking.recordUsage(userId, actionType, metadata, planName);

            console.log(`Usage recorded: User ${userId} performed ${actionType} (${planName} plan)`);
            return true;
        } catch (error) {
            console.error('Error recording usage:', error);
            return false;
        }
    }

    /**
     * Get user's remaining limits
     */
    static async getUserRemainingLimits(userId) {
        try {
            const { planName, limits } = await this.getUserLimits(userId);
            
            const remaining = {};
            
            for (const [actionType, actionLimits] of Object.entries(limits)) {
                if (typeof actionLimits === 'object' && actionLimits !== null) {
                    remaining[actionType] = {};
                    
                    for (const [period, limit] of Object.entries(actionLimits)) {
                        if (typeof limit === 'number') {
                            if (limit === -1) {
                                remaining[actionType][period] = -1; // Unlimited
                            } else {
                                const currentUsage = await this.getCurrentUsage(userId, actionType, period);
                                remaining[actionType][period] = Math.max(0, limit - currentUsage);
                            }
                        }
                    }
                } else {
                    // For boolean features
                    remaining[actionType] = actionLimits;
                }
            }
            
            return { planName, remaining };
        } catch (error) {
            console.error('Error getting user remaining limits:', error);
            throw error;
        }
    }

    /**
     * Middleware function to check limits before allowing actions
     */
    static checkLimitMiddleware(actionType, period = 'monthly') {
        return async (req, res, next) => {
            try {
                const userId = req.user?._id || req.user?.id;
                
                if (!userId) {
                    return res.status(401).json({ error: 'Authentication required' });
                }

                const canPerform = await this.canUserPerformAction(userId, actionType, period);
                
                if (!canPerform) {
                    const { planName, limits } = await this.getUserLimits(userId);
                    const limit = limits[actionType]?.[period];
                    
                    return res.status(403).json({ 
                        error: `${actionType} limit exceeded`,
                        details: {
                            action: actionType,
                            period,
                            limit: limit === -1 ? 'unlimited' : limit,
                            planName,
                            message: `Your ${planName} plan allows ${limit === -1 ? 'unlimited' : limit} ${actionType} per ${period}. Please upgrade your plan to continue.`
                        }
                    });
                }

                // Store action info in request for potential usage recording
                req.subscriptionAction = { actionType, period };
                next();
            } catch (error) {
                console.error('Subscription limit check error:', error);
                res.status(500).json({ error: 'Failed to check subscription limits' });
            }
        };
    }

    /**
     * Middleware to record usage after successful action
     */
    static recordUsageMiddleware() {
        return async (req, res, next) => {
            // Store original res.json to intercept successful responses
            const originalJson = res.json;
            
            res.json = function(data) {
                // Only record usage on successful responses (2xx status codes)
                if (res.statusCode >= 200 && res.statusCode < 300 && req.subscriptionAction) {
                    const userId = req.user?._id || req.user?.id;
                    if (userId) {
                        SubscriptionService.recordUsage(
                            userId, 
                            req.subscriptionAction.actionType,
                            { 
                                endpoint: req.originalUrl,
                                method: req.method,
                                timestamp: new Date()
                            }
                        ).catch(err => {
                            console.error('Failed to record usage:', err);
                        });
                    }
                }
                
                // Call original res.json
                return originalJson.call(this, data);
            };
            
            next();
        };
    }
}

export default SubscriptionService;
