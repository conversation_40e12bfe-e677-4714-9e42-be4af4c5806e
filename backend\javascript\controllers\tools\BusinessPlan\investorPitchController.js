// src/controllers/Tools/Business/investorPitchController.js
import { generateContent } from '../../../services/geminiBusinessService.js';
import { buildInvestorPitchPrompt } from './investorPitchPrompts.js';
import User from '../../../models/User.js';
import SubscriptionService from '../../../services/subscriptionService.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Placeholder text extraction function...
const extractTextFromBusinessPlan = async (file) => {
    // ... function content remains the same
};

export const generateInvestorPitch = async (req, res) => {
    // ... validation logic remains the same

    try {
        console.log(`[PITCH_CONTROLLER] Generating investor pitch for project: "${req.body.projectName}"`);

        const user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Check subscription limits using the new dynamic service
        const canPerform = await SubscriptionService.canUserPerformAction(
            req.user.id,
            'investorPitches',
            'monthly'
        );

        if (!canPerform) {
            const { planName, limits } = await SubscriptionService.getUserLimits(req.user.id);
            const limit = limits.investorPitches?.monthly;

            return res.status(403).json({
                error: `${planName} plan investor pitch limit reached`,
                details: {
                    limit: limit === -1 ? 'unlimited' : limit,
                    planName,
                    message: `Your ${planName} plan allows ${limit === -1 ? 'unlimited' : limit} investor pitches per month. Please upgrade for more access.`
                }
            });
        }

        let businessPlanContent = null;
        if (req.file) {
            businessPlanContent = await extractTextFromBusinessPlan(req.file);
        }
        
        const enhancedFormData = { ...req.body, businessPlanContent };
        const pitchPrompt = buildInvestorPitchPrompt(enhancedFormData);
        const generatedPitch = await generateContent(pitchPrompt);

        // Record usage with the new tracking system
        await SubscriptionService.recordUsage(
            req.user.id,
            'investorPitches',
            {
                projectName: req.body.projectName,
                industry: req.body.industry,
                endpoint: req.originalUrl,
                method: req.method,
                success: true
            }
        );

        // Get updated user limits for response
        const { planName, remaining } = await SubscriptionService.getUserRemainingLimits(req.user.id);

        res.status(200).json({
            generatedPitch,
            subscription: {
                planName,
                remaining: remaining.investorPitches
            }
        });

    } catch (error) {
        console.error(`[PITCH_CONTROLLER] Error during pitch generation:`, error.message);
        res.status(500).json({
            error: 'An internal server error occurred while generating the investor pitch.'
        });
    }
};