// controllers/businessPlan/PlanGeneratorController.js
import { generateContent } from '../../../services/geminiBusinessService.js';
import { buildFullBusinessPlanPrompt } from './PlanGenratorPrompts.js';
import axios from 'axios';
import User from '../../../models/User.js';
import SubscriptionService from '../../../services/subscriptionService.js';

const fetchMarketData = async (query) => {
    if (!query) return [];
    try {
        const response = await axios.request({
            method: 'GET',
            url: 'https://dataservices-market-research-library-v1.p.rapidapi.com/api.trade.gov/v2/market_research_library/search',
            params: { q: query, limit: 5 },
            headers: {
                'x-rapidapi-key': process.env.RAPIDAPI_KEY,
                'x-rapidapi-host': 'dataservices-market-research-library-v1.p.rapidapi.com'
            }
        });
        return response.data?.results || [];
    } catch (error) {
        console.error('RapidAPI Market Data Request Failed:', error.message);
        return [];
    }
};

export const generateFullBusinessReport = async (req, res) => {
    // 1. Fetch user and validate form data
    const user = await User.findById(req.user.id);
    if (!user) {
        return res.status(404).json({ error: 'User not found.' });
    }

    const formData = req.body;
    const { planType, idea, audience, profit, problem, language, period, budget, industry } = formData;

    if (!planType || !language || !period) {
        return res.status(400).json({ error: 'Core planning parameters are missing.' });
    }
    if (planType === 'user' && (!idea || !audience || !profit || !problem)) {
        return res.status(400).json({ error: 'A complete business context is required to generate a plan.' });
    } else if (planType === 'ai' && (!budget || !industry)) {
        return res.status(400).json({ error: 'Investment Budget and Industry are required for the AI to generate an idea.' });
    }

    // 2. Check usage limit using dynamic subscription service
    const canPerform = await SubscriptionService.canUserPerformAction(
        req.user.id,
        'businessPlans',
        'monthly'
    );

    if (!canPerform) {
        const { planName, limits } = await SubscriptionService.getUserLimits(req.user.id);
        const limit = limits.businessPlans?.monthly;

        return res.status(403).json({
            error: `${planName} plan business plan limit reached`,
            details: {
                limit: limit === -1 ? 'unlimited' : limit,
                planName,
                message: `Your ${planName} plan allows ${limit === -1 ? 'unlimited' : limit} business plans per month. Please upgrade for more access.`
            }
        });
    }

    // 3. Generate the report
    try {
        console.log(`[CONTROLLER] Generating new report for planType: "${planType}" for user: ${user.email}`);
        
        const marketQuery = planType === 'ai' ? industry : idea;
        const marketData = await fetchMarketData(marketQuery);
        
        const finalPrompt = buildFullBusinessPlanPrompt(formData, marketData);
        
        const fullReportText = await generateContent(finalPrompt);

        // 4. Record usage with the new tracking system
        await SubscriptionService.recordUsage(
            req.user.id,
            'businessPlans',
            {
                businessName: req.body.businessName,
                industry: req.body.industry,
                endpoint: req.originalUrl,
                method: req.method,
                success: true
            }
        );

        console.log(`[CONTROLLER] Successfully recorded business plan usage for user: ${user.email}`);

        // 5. Get updated user limits for response
        const { planName, remaining } = await SubscriptionService.getUserRemainingLimits(req.user.id);

        // 6. Send response with report and updated subscription
        res.status(200).json({
            fullBusinessReport: fullReportText,
            subscription: {
                planName,
                remaining: remaining.businessPlans
            }
        });

    } catch (error) {
        console.error(`Controller Error during report generation for user ${user.email}:`, error.message);
        res.status(500).json({
            error: error.message || 'An internal server error occurred while generating the report.'
        });
    }
};