// src/components/admin/PaymentManagement.jsx
import React, { useState, useEffect } from 'react';
import { 
  FiCreditCard, 
  FiLoader,
  FiSave,
  FiRefreshCw,
  FiDollarSign,
  FiUsers,
  FiTrendingUp,
  FiSettings,
  FiCheckCircle,
  FiAlertTriangle
} from 'react-icons/fi';

const PaymentManagement = () => {
  const [plans, setPlans] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('limits');

  const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

  const fetchData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      
      // Fetch subscription plans
      const plansResponse = await fetch(`${API_BASE_URL}/api/admin/subscription-plans`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!plansResponse.ok) {
        throw new Error('Failed to fetch subscription plans');
      }

      const plansData = await plansResponse.json();
      setPlans(plansData.plans);

      // Fetch subscription stats
      const statsResponse = await fetch(`${API_BASE_URL}/api/admin/subscription-stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleLimitChange = (planName, category, limitType, value) => {
    setPlans(prevPlans => 
      prevPlans.map(plan => 
        plan.planName === planName 
          ? {
              ...plan,
              limits: {
                ...plan.limits,
                [category]: {
                  ...plan.limits[category],
                  [limitType]: parseInt(value) || 0
                }
              }
            }
          : plan
      )
    );
  };

  const handlePriceChange = (planName, priceType, value) => {
    setPlans(prevPlans => 
      prevPlans.map(plan => 
        plan.planName === planName 
          ? {
              ...plan,
              price: {
                ...plan.price,
                [priceType]: parseFloat(value) || 0
              }
            }
          : plan
      )
    );
  };

  const handleBooleanChange = (planName, category, value) => {
    setPlans(prevPlans => 
      prevPlans.map(plan => 
        plan.planName === planName 
          ? {
              ...plan,
              limits: {
                ...plan.limits,
                [category]: value
              }
            }
          : plan
      )
    );
  };

  const savePlan = async (plan) => {
    try {
      setSaving(true);
      setError('');
      setSuccess('');
      
      const token = localStorage.getItem('authToken');
      
      const response = await fetch(`${API_BASE_URL}/api/admin/subscription-plans/${plan.planName}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(plan),
      });

      if (!response.ok) {
        throw new Error('Failed to save plan');
      }

      setSuccess(`${plan.planName} plan saved successfully!`);
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  const resetPlan = async (planName) => {
    if (!window.confirm(`Are you sure you want to reset ${planName} plan to default values?`)) {
      return;
    }

    try {
      setSaving(true);
      const token = localStorage.getItem('authToken');
      
      const response = await fetch(`${API_BASE_URL}/api/admin/subscription-plans/${planName}/reset`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to reset plan');
      }

      await fetchData(); // Refresh data
      setSuccess(`${planName} plan reset to default values!`);
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <FiLoader className="w-8 h-8 text-purple-500 animate-spin" />
      </div>
    );
  }

  const tabs = [
    { id: 'limits', label: 'Plan Limits', icon: FiSettings },
    { id: 'pricing', label: 'Pricing', icon: FiDollarSign },
    { id: 'stats', label: 'Statistics', icon: FiTrendingUp },
  ];

  return (
    <div className="h-full flex flex-col">
      <div className="flex-shrink-0 p-6 border-b border-slate-700">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FiCreditCard className="w-6 h-6 text-purple-500 mr-3" />
            <h2 className="text-xl font-bold text-white">Payment & Subscription Management</h2>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6">

      {/* Status Messages */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-300 flex items-center">
          <FiAlertTriangle className="w-4 h-4 mr-2" />
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-900/50 border border-green-700 rounded-lg text-green-300 flex items-center">
          <FiCheckCircle className="w-4 h-4 mr-2" />
          {success}
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-slate-800 p-1 rounded-lg">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'text-slate-300 hover:text-white hover:bg-slate-700'
              }`}
            >
              <Icon className="w-4 h-4 mr-2" />
              {tab.label}
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      {activeTab === 'limits' && (
        <div className="space-y-6">
          {plans.map((plan) => (
            <div key={plan.planName} className="bg-slate-700 rounded-lg p-6 border border-slate-600">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  {plan.displayName}
                  <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium ${
                    plan.planName === 'Starter' ? 'bg-blue-900/50 text-blue-300' :
                    plan.planName === 'Pro' ? 'bg-purple-900/50 text-purple-300' :
                    'bg-yellow-900/50 text-yellow-300'
                  }`}>
                    {plan.planName}
                  </span>
                </h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => savePlan(plan)}
                    disabled={saving}
                    className="flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white rounded text-sm transition-colors"
                  >
                    <FiSave className="w-3 h-3 mr-1" />
                    Save
                  </button>
                  <button
                    onClick={() => resetPlan(plan.planName)}
                    disabled={saving}
                    className="flex items-center px-3 py-1 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 text-white rounded text-sm transition-colors"
                  >
                    <FiRefreshCw className="w-3 h-3 mr-1" />
                    Reset
                  </button>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {/* PDF Uploads */}
                <div className="bg-slate-800 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-slate-300 mb-3">PDF Uploads</h4>
                  <div className="space-y-2">
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Monthly Limit</label>
                      <input
                        type="number"
                        value={plan.limits.pdfUploads.monthly}
                        onChange={(e) => handleLimitChange(plan.planName, 'pdfUploads', 'monthly', e.target.value)}
                        className="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm"
                        placeholder="Unlimited = -1"
                      />
                    </div>
                  </div>
                </div>

                {/* Business Plans */}
                <div className="bg-slate-800 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-slate-300 mb-3">Business Plans</h4>
                  <div className="space-y-2">
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Monthly Limit</label>
                      <input
                        type="number"
                        value={plan.limits.businessPlans.monthly}
                        onChange={(e) => handleLimitChange(plan.planName, 'businessPlans', 'monthly', e.target.value)}
                        className="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm"
                        placeholder="Unlimited = -1"
                      />
                    </div>
                  </div>
                </div>

                {/* Investor Pitches */}
                <div className="bg-slate-800 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-slate-300 mb-3">Investor Pitches</h4>
                  <div className="space-y-2">
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Monthly Limit</label>
                      <input
                        type="number"
                        value={plan.limits.investorPitches.monthly}
                        onChange={(e) => handleLimitChange(plan.planName, 'investorPitches', 'monthly', e.target.value)}
                        className="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm"
                        placeholder="Unlimited = -1"
                      />
                    </div>
                  </div>
                </div>

                {/* Business Q&A */}
                <div className="bg-slate-800 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-slate-300 mb-3">Business Q&A</h4>
                  <div className="space-y-2">
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Daily Limit</label>
                      <input
                        type="number"
                        value={plan.limits.businessQA.daily}
                        onChange={(e) => handleLimitChange(plan.planName, 'businessQA', 'daily', e.target.value)}
                        className="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm"
                        placeholder="Unlimited = -1"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Monthly Limit</label>
                      <input
                        type="number"
                        value={plan.limits.businessQA.monthly}
                        onChange={(e) => handleLimitChange(plan.planName, 'businessQA', 'monthly', e.target.value)}
                        className="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm"
                        placeholder="Unlimited = -1"
                      />
                    </div>
                  </div>
                </div>

                {/* Chat Messages */}
                <div className="bg-slate-800 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-slate-300 mb-3">Chat Messages</h4>
                  <div className="space-y-2">
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Daily Limit</label>
                      <input
                        type="number"
                        value={plan.limits.chatMessages.daily}
                        onChange={(e) => handleLimitChange(plan.planName, 'chatMessages', 'daily', e.target.value)}
                        className="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm"
                        placeholder="Unlimited = -1"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Monthly Limit</label>
                      <input
                        type="number"
                        value={plan.limits.chatMessages.monthly}
                        onChange={(e) => handleLimitChange(plan.planName, 'chatMessages', 'monthly', e.target.value)}
                        className="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm"
                        placeholder="Unlimited = -1"
                      />
                    </div>
                  </div>
                </div>

                {/* Storage */}
                <div className="bg-slate-800 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-slate-300 mb-3">Storage (MB)</h4>
                  <div className="space-y-2">
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Total Storage</label>
                      <input
                        type="number"
                        value={plan.limits.storage.total}
                        onChange={(e) => handleLimitChange(plan.planName, 'storage', 'total', e.target.value)}
                        className="w-full px-2 py-1 bg-slate-700 border border-slate-600 rounded text-white text-sm"
                        placeholder="In MB"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Feature Toggles */}
              <div className="mt-4 pt-4 border-t border-slate-600">
                <h4 className="text-sm font-medium text-slate-300 mb-3">Premium Features</h4>
                <div className="grid gap-3 md:grid-cols-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={plan.limits.advancedAnalytics}
                      onChange={(e) => handleBooleanChange(plan.planName, 'advancedAnalytics', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm text-slate-300">Advanced Analytics</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={plan.limits.prioritySupport}
                      onChange={(e) => handleBooleanChange(plan.planName, 'prioritySupport', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm text-slate-300">Priority Support</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={plan.limits.customBranding}
                      onChange={(e) => handleBooleanChange(plan.planName, 'customBranding', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm text-slate-300">Custom Branding</span>
                  </label>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'pricing' && (
        <div className="space-y-6">
          {plans.map((plan) => (
            <div key={plan.planName} className="bg-slate-700 rounded-lg p-6 border border-slate-600">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">{plan.displayName}</h3>
                <button
                  onClick={() => savePlan(plan)}
                  disabled={saving}
                  className="flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white rounded text-sm transition-colors"
                >
                  <FiSave className="w-3 h-3 mr-1" />
                  Save Pricing
                </button>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Monthly Price ($)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={plan.price.monthly}
                    onChange={(e) => handlePriceChange(plan.planName, 'monthly', e.target.value)}
                    className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded text-white"
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Yearly Price ($)</label>
                  <input
                    type="number"
                    step="0.01"
                    value={plan.price.yearly}
                    onChange={(e) => handlePriceChange(plan.planName, 'yearly', e.target.value)}
                    className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded text-white"
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-slate-300 mb-2">Description</label>
                <textarea
                  value={plan.description}
                  onChange={(e) => setPlans(prevPlans => 
                    prevPlans.map(p => 
                      p.planName === plan.planName 
                        ? { ...p, description: e.target.value }
                        : p
                    )
                  )}
                  className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded text-white"
                  rows="2"
                  placeholder="Plan description..."
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'stats' && stats && (
        <div className="space-y-6">
          {/* Overview Stats */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
              <div className="flex items-center">
                <FiUsers className="w-8 h-8 text-blue-400 mr-3" />
                <div>
                  <div className="text-2xl font-bold text-white">{stats.totalUsers}</div>
                  <div className="text-sm text-slate-400">Total Users</div>
                </div>
              </div>
            </div>

            <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
              <div className="flex items-center">
                <FiCheckCircle className="w-8 h-8 text-green-400 mr-3" />
                <div>
                  <div className="text-2xl font-bold text-white">{stats.activeUsers}</div>
                  <div className="text-sm text-slate-400">Active Users</div>
                </div>
              </div>
            </div>

            <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
              <div className="flex items-center">
                <FiDollarSign className="w-8 h-8 text-green-400 mr-3" />
                <div>
                  <div className="text-2xl font-bold text-white">${stats.estimatedMonthlyRevenue.toFixed(2)}</div>
                  <div className="text-sm text-slate-400">Est. Monthly Revenue</div>
                </div>
              </div>
            </div>

            <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
              <div className="flex items-center">
                <FiTrendingUp className="w-8 h-8 text-purple-400 mr-3" />
                <div>
                  <div className="text-2xl font-bold text-white">{stats.planDistribution.length}</div>
                  <div className="text-sm text-slate-400">Active Plans</div>
                </div>
              </div>
            </div>
          </div>

          {/* Plan Distribution */}
          <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
            <h3 className="text-lg font-semibold text-white mb-4">Plan Distribution</h3>
            <div className="space-y-4">
              {stats.planDistribution.map((planStat) => (
                <div key={planStat.planName} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-4 h-4 rounded-full mr-3 ${
                      planStat.planName === 'Starter' ? 'bg-blue-500' :
                      planStat.planName === 'Pro' ? 'bg-purple-500' :
                      'bg-yellow-500'
                    }`}></div>
                    <span className="text-white font-medium">{planStat.planName || 'Starter'}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-white font-medium">{planStat.userCount} users</div>
                    <div className="text-sm text-slate-400">{planStat.verifiedUsers} verified</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
        </div>
      </div>
    </div>
  );
};

export default PaymentManagement;
