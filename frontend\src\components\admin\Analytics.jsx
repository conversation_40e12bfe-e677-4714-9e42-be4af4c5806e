// src/components/admin/Analytics.jsx
import React, { useState, useEffect } from 'react';
import { 
  <PERSON>B<PERSON><PERSON><PERSON>, 
  FiUsers, 
  FiUser<PERSON>heck, 
  FiUserX, 
  FiShield,
  FiTrendingUp,
  FiLoader
} from 'react-icons/fi';

const Analytics = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      
      const response = await fetch(`${API_BASE_URL}/api/admin/analytics`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }

      const data = await response.json();
      setAnalytics(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, []);

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <FiLoader className="w-8 h-8 text-purple-500 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="p-4 bg-red-900/50 border border-red-700 rounded-lg text-red-300">
          Error loading analytics: {error}
        </div>
      </div>
    );
  }

  const { userStats, systemInfo } = analytics;

  const statCards = [
    {
      title: 'Total Users',
      value: userStats.totalUsers,
      icon: FiUsers,
      color: 'blue',
      description: 'All registered users'
    },
    {
      title: 'Verified Users',
      value: userStats.verifiedUsers,
      icon: FiUserCheck,
      color: 'green',
      description: 'Email verified users'
    },
    {
      title: 'Unverified Users',
      value: userStats.unverifiedUsers,
      icon: FiUserX,
      color: 'yellow',
      description: 'Pending email verification'
    },
    {
      title: 'Administrators',
      value: userStats.adminUsers,
      icon: FiShield,
      color: 'purple',
      description: 'Admin users'
    },
    {
      title: 'Recent Registrations',
      value: userStats.recentRegistrations,
      icon: FiTrendingUp,
      color: 'pink',
      description: 'Last 30 days'
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-900/50 text-blue-300 border-blue-700',
      green: 'bg-green-900/50 text-green-300 border-green-700',
      yellow: 'bg-yellow-900/50 text-yellow-300 border-yellow-700',
      purple: 'bg-purple-900/50 text-purple-300 border-purple-700',
      pink: 'bg-pink-900/50 text-pink-300 border-pink-700'
    };
    return colors[color] || colors.blue;
  };

  const verificationRate = userStats.totalUsers > 0 
    ? ((userStats.verifiedUsers / userStats.totalUsers) * 100).toFixed(1)
    : 0;

  return (
    <div className="h-full flex flex-col">
      <div className="flex-shrink-0 p-6 border-b border-slate-700">
        {/* Header */}
        <div className="flex items-center">
          <FiBarChart className="w-6 h-6 text-purple-500 mr-3" />
          <h2 className="text-xl font-bold text-white">System Analytics</h2>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6">

      {/* Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 mb-8">
        {statCards.map((stat) => {
          const Icon = stat.icon;
          return (
            <div
              key={stat.title}
              className={`p-6 rounded-lg border ${getColorClasses(stat.color)}`}
            >
              <div className="flex items-center justify-between mb-4">
                <Icon className="w-8 h-8" />
                <span className="text-2xl font-bold">{stat.value}</span>
              </div>
              <h3 className="font-medium mb-1">{stat.title}</h3>
              <p className="text-xs opacity-75">{stat.description}</p>
            </div>
          );
        })}
      </div>

      {/* Detailed Analytics */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* User Statistics */}
        <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
          <h3 className="text-lg font-semibold text-white mb-4">User Statistics</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-slate-300">Verification Rate</span>
              <span className="text-white font-medium">{verificationRate}%</span>
            </div>
            
            <div className="w-full bg-slate-600 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${verificationRate}%` }}
              ></div>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {userStats.verifiedUsers}
                </div>
                <div className="text-sm text-slate-400">Verified</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">
                  {userStats.unverifiedUsers}
                </div>
                <div className="text-sm text-slate-400">Unverified</div>
              </div>
            </div>
          </div>
        </div>

        {/* System Information */}
        <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
          <h3 className="text-lg font-semibold text-white mb-4">System Information</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-slate-300">Admin System Status</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                systemInfo.hasInitialAdmin 
                  ? 'bg-green-900/50 text-green-300' 
                  : 'bg-red-900/50 text-red-300'
              }`}>
                {systemInfo.hasInitialAdmin ? 'Active' : 'Inactive'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-slate-300">System Administrator</span>
              <span className="text-white text-sm">
                {systemInfo.systemAdminEmail || 'Not set'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-slate-300">Admin Registration</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                systemInfo.allowNewAdminRegistration 
                  ? 'bg-green-900/50 text-green-300' 
                  : 'bg-red-900/50 text-red-300'
              }`}>
                {systemInfo.allowNewAdminRegistration ? 'Enabled' : 'Disabled'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-slate-300">Maintenance Mode</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                systemInfo.maintenanceMode 
                  ? 'bg-yellow-900/50 text-yellow-300' 
                  : 'bg-green-900/50 text-green-300'
              }`}>
                {systemInfo.maintenanceMode ? 'On' : 'Off'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Growth Metrics */}
      <div className="mt-6 bg-slate-700 rounded-lg p-6 border border-slate-600">
        <h3 className="text-lg font-semibold text-white mb-4">Growth Metrics</h3>
        
        <div className="grid gap-4 md:grid-cols-3">
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">
              {userStats.recentRegistrations}
            </div>
            <div className="text-slate-300">New Users</div>
            <div className="text-sm text-slate-400">Last 30 days</div>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">
              {userStats.totalUsers > 0 ? Math.round(userStats.recentRegistrations / 30 * 10) / 10 : 0}
            </div>
            <div className="text-slate-300">Daily Average</div>
            <div className="text-sm text-slate-400">New registrations</div>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400 mb-2">
              {userStats.totalUsers > 0 ? Math.round((userStats.recentRegistrations / userStats.totalUsers) * 100) : 0}%
            </div>
            <div className="text-slate-300">Growth Rate</div>
            <div className="text-sm text-slate-400">Recent vs total</div>
          </div>
        </div>
      </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
