// src/controllers/Tools/Business/businessQAController.js

import { generateContent } from '../../../services/geminiBusinessService.js';
import { buildBusinessQAPrompt } from './businessQAPrompts.js';
import User from '../../../models/User.js';
import SubscriptionService from '../../../services/subscriptionService.js';

/**
 * Controller to handle business Q&A generation.
 */
export const generateBusinessQA = async (req, res) => {
    // 1. Extract and validate form data from the request body
    const {
        question,
        businessContext,
        industry,
        businessStage,
        specificArea
    } = req.body;

    if (!question || question.trim().length === 0) {
        return res.status(400).json({
            error: 'Question is required. Please provide a business question.'
        });
    }

    if (question.length > 1000) {
        return res.status(400).json({
            error: 'Question is too long. Please limit your question to 1000 characters.'
        });
    }

    try {
        console.log(`[BUSINESS_QA_CONTROLLER] Generating answer for question: "${question.substring(0, 50)}..."`);

        // 2. Check user limits before generating
        const user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Check subscription limits using the new dynamic service
        const canPerform = await SubscriptionService.canUserPerformAction(
            req.user.id,
            'businessQA',
            'daily'
        );

        if (!canPerform) {
            const { planName, limits } = await SubscriptionService.getUserLimits(req.user.id);
            const limit = limits.businessQA?.daily;

            return res.status(403).json({
                error: `${planName} plan business Q&A limit reached`,
                details: {
                    limit: limit === -1 ? 'unlimited' : limit,
                    planName,
                    message: `Your ${planName} plan allows ${limit === -1 ? 'unlimited' : limit} business Q&A per day. Please upgrade for more access.`
                }
            });
        }

        // 3. Build the advanced prompt using the dedicated prompt-builder function
        const qaPrompt = buildBusinessQAPrompt(req.body);

        // 4. Call the generic Gemini service to generate the content
        const generatedAnswer = await generateContent(qaPrompt);

        // 5. Record usage with the new tracking system
        await SubscriptionService.recordUsage(
            req.user.id,
            'businessQA',
            {
                question: question.substring(0, 100), // Store first 100 chars of question
                industry: req.body.industry,
                endpoint: req.originalUrl,
                method: req.method,
                success: true
            }
        );

        // 6. Get updated user limits for response
        const { planName, remaining } = await SubscriptionService.getUserRemainingLimits(req.user.id);

        // 7. Send the successful response back to the client
        res.status(200).json({
            generatedAnswer,
            subscription: {
                planName,
                remaining: remaining.businessQA
            }
        });

    } catch (error) {
        console.error(`[BUSINESS_QA_CONTROLLER] Error during Q&A generation:`, error.message);
        res.status(500).json({
            error: 'An internal server error occurred while generating the business answer.'
        });
    }
};